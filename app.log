2025-05-05 18:47:39,736 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-05-05 18:47:40,095 - Worker - INFO - Em<PERSON> using >> nancy_ol9s8_4@gmx.<NAME_EMAIL>
2025-05-05 18:48:10,994 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-05-05 18:48:11,354 - Worker - INFO - Email <PERSON> using >> <EMAIL> to y<PERSON><PERSON><PERSON><PERSON>@hotmail.com
2025-05-07 00:06:39,006 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 00:06:39,012 - VPNManager - INFO - Turning on VPN...
2025-05-07 00:06:39,012 - VP<PERSON><PERSON>anager - DEBUG - HMA VPN executable found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 00:06:39,012 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 1/3)
2025-05-07 00:06:39,064 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:06:39,065 - VPNManager - WARNING - Failed to turn on VPN (attempt 1/3)
2025-05-07 00:06:39,065 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:06:44,080 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 2/3)
2025-05-07 00:06:44,126 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:06:44,127 - VPNManager - WARNING - Failed to turn on VPN (attempt 2/3)
2025-05-07 00:06:44,127 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:06:49,130 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 3/3)
2025-05-07 00:06:49,179 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:06:49,179 - VPNManager - WARNING - Failed to turn on VPN (attempt 3/3)
2025-05-07 00:06:49,180 - VPNManager - ERROR - Failed to turn on VPN after multiple attempts
2025-05-07 00:11:08,190 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 00:11:08,194 - VPNManager - INFO - Turning on VPN...
2025-05-07 00:11:08,194 - VPNManager - DEBUG - HMA VPN executable found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 00:11:08,194 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 1/3)
2025-05-07 00:11:08,242 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:11:08,243 - VPNManager - WARNING - Failed to turn on VPN (attempt 1/3)
2025-05-07 00:11:08,243 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:11:13,253 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 2/3)
2025-05-07 00:11:13,304 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:11:13,304 - VPNManager - WARNING - Failed to turn on VPN (attempt 2/3)
2025-05-07 00:11:13,304 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:11:18,310 - VPNManager - DEBUG - Attempting to turn on VPN (attempt 3/3)
2025-05-07 00:11:18,355 - VPNManager - DEBUG - run_hma_command('turn_on') returned: False
2025-05-07 00:11:18,355 - VPNManager - WARNING - Failed to turn on VPN (attempt 3/3)
2025-05-07 00:11:18,355 - VPNManager - ERROR - Failed to turn on VPN after multiple attempts
2025-05-07 00:11:30,623 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-05-07 00:12:18,058 - App - INFO - Application closing - cleaning up threads
2025-05-07 00:12:18,058 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-05-07 00:21:26,280 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 00:21:26,284 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:26,308 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:26,415 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:26,416 - VPNManager - INFO - VPN is already connected according to network check
2025-05-07 00:21:26,416 - VPNManager - DEBUG - Getting current public IP address...
2025-05-07 00:21:27,383 - VPNManager - DEBUG - Current IP address: **************
2025-05-07 00:21:36,664 - VPNManager - DEBUG - Getting current public IP address...
2025-05-07 00:21:36,963 - VPNManager - DEBUG - Current IP address: **************
2025-05-07 00:21:36,963 - VPNManager - DEBUG - ensure_vpn_off called, current state: vpn_enabled=True
2025-05-07 00:21:36,963 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:36,986 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:37,061 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:37,061 - VPNManager - INFO - VPN is still connected, attempting to disconnect...
2025-05-07 00:21:37,061 - VPNManager - DEBUG - HMA VPN executable found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 00:21:37,061 - VPNManager - DEBUG - Attempting to turn off VPN (attempt 1/3)
2025-05-07 00:21:37,369 - VPNManager - DEBUG - run_hma_command('turn_off') returned: True
2025-05-07 00:21:37,369 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:37,391 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:37,466 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:37,467 - VPNManager - WARNING - Failed to turn off VPN (attempt 1/3)
2025-05-07 00:21:37,467 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:21:42,481 - VPNManager - DEBUG - Attempting to turn off VPN (attempt 2/3)
2025-05-07 00:21:42,790 - VPNManager - DEBUG - run_hma_command('turn_off') returned: True
2025-05-07 00:21:42,790 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:42,812 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:42,923 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:42,923 - VPNManager - WARNING - Failed to turn off VPN (attempt 2/3)
2025-05-07 00:21:42,923 - VPNManager - DEBUG - Waiting 5 seconds before next attempt
2025-05-07 00:21:47,924 - VPNManager - DEBUG - Attempting to turn off VPN (attempt 3/3)
2025-05-07 00:21:48,232 - VPNManager - DEBUG - run_hma_command('turn_off') returned: True
2025-05-07 00:21:48,232 - VPNManager - DEBUG - Checking actual VPN connection status...
2025-05-07 00:21:48,256 - VPNManager - DEBUG - Found VPN indicator 'hma' in network interfaces
2025-05-07 00:21:48,361 - VPNManager - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 00:21:48,361 - VPNManager - WARNING - Failed to turn off VPN (attempt 3/3)
2025-05-07 00:21:48,361 - VPNManager - ERROR - Failed to turn off VPN after multiple attempts
2025-05-07 00:27:21,928 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-05-07 00:28:42,516 - App - INFO - Application closing - cleaning up threads
2025-05-07 00:28:42,516 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-05-07 17:12:41,950 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:13:20,365 - App - WARNING - Health check thread did not terminate within timeout
2025-05-07 17:16:45,910 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:16:57,545 - App - WARNING - Health check thread did not terminate within timeout
2025-05-07 17:23:46,058 - VPNManager - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:29:04,054 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 17:29:04,054 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 17:29:04,054 - SimpleVPN - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:29:04,129 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 17:29:04,129 - SimpleVPN - INFO - VPN is already connected
2025-05-07 17:29:04,129 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:29:04,523 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:29:09,256 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:29:09,536 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:29:09,536 - SimpleVPN - DEBUG - ensure_vpn_off called, current state: vpn_enabled=True
2025-05-07 17:29:09,604 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 17:34:46,391 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 17:34:46,391 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 17:34:46,393 - SimpleVPN - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-05-07 17:34:46,470 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 17:34:46,470 - SimpleVPN - INFO - VPN is already connected
2025-05-07 17:34:46,470 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:34:46,812 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:34:50,297 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:34:50,584 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:34:50,584 - SimpleVPN - DEBUG - ensure_vpn_off called, current state: vpn_enabled=True
2025-05-07 17:34:50,652 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-05-07 17:34:53,510 - App - WARNING - Health check thread did not terminate within timeout
2025-05-07 17:43:34,503 - App - INFO - pywinauto is installed
2025-05-07 17:43:37,729 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 17:43:37,729 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 17:44:03,023 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-05-07 17:44:04,891 - App - INFO - Application closing - cleaning up threads
2025-05-07 17:44:04,891 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-05-07 17:47:52,287 - App - INFO - pywinauto is installed
2025-05-07 17:47:54,581 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 17:47:54,581 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 17:48:03,800 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:48:04,147 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:48:16,364 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:48:16,943 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 17:48:36,770 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:48:37,143 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:48:51,828 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:48:52,392 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 17:49:11,856 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:49:12,206 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:49:26,668 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:49:27,230 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 17:49:46,588 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:49:46,947 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 17:50:01,319 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 17:50:01,769 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 17:50:03,472 - App - INFO - Application closing - cleaning up threads
2025-05-07 17:50:03,472 - App - INFO - Stopping health check thread
2025-05-07 20:38:02,242 - App - INFO - pywinauto is installed
2025-05-07 20:38:06,275 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-05-07 20:38:06,283 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-05-07 20:38:15,982 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:38:16,447 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:38:26,186 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:38:26,783 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:38:46,594 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:38:46,985 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:39:04,079 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:39:04,762 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:39:24,107 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:39:24,484 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:39:38,802 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:39:39,245 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:39:58,702 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:39:59,072 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:40:18,309 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:40:18,915 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:40:38,269 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:40:38,905 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:41:02,746 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:41:03,196 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:41:22,371 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:41:22,726 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:41:39,747 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:41:40,340 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:41:59,603 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:42:00,128 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:42:18,555 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:42:19,156 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:42:38,577 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:42:38,932 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:42:53,857 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:42:54,453 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:43:13,826 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:43:14,203 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:43:29,357 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:43:30,442 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:43:50,032 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:43:50,426 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:44:06,323 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:44:06,895 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:44:26,192 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:44:26,667 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:44:43,107 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:44:44,106 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:45:03,638 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:45:04,028 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:45:22,364 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:45:22,979 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:45:42,288 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:45:42,650 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:45:57,853 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:45:58,318 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:46:17,647 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:46:18,020 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:46:58,540 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:46:59,132 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:47:18,824 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:47:19,192 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:47:34,273 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:47:34,860 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:47:54,341 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:47:54,719 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:49:08,559 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:49:09,117 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:49:28,430 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:49:28,865 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:49:45,649 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:49:46,213 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:50:05,689 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:50:06,082 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:50:20,424 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:50:20,892 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:50:40,321 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:50:40,661 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:52:42,713 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:52:43,317 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:53:02,638 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:53:03,047 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:53:42,751 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:53:43,377 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:54:02,625 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:54:02,970 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 20:54:07,759 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:54:08,318 - SimpleVPN - DEBUG - Current IP address: ***********
2025-05-07 20:54:19,093 - SimpleVPN - DEBUG - Getting current public IP address...
2025-05-07 20:54:19,482 - SimpleVPN - DEBUG - Current IP address: **************
2025-05-07 21:03:14,701 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-05-07 21:11:13,435 - App - INFO - Application closing - cleaning up threads
2025-05-07 21:11:13,435 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-05-07 21:11:13,435 - App - ERROR - Error cleaning up export thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 22:19:49,545 - App - INFO - pywinauto is installed
2025-06-14 22:19:56,405 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:19:56,405 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:20:02,663 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:20:03,014 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 22:20:03,667 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:20:03,667 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:20:03,670 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:20:04,603 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:20:04,604 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:20:04,604 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:20:04,605 - urllib3.connectionpool - DEBUG - Resetting dropped connection: login.microsoftonline.com
2025-06-14 22:20:05,620 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:20:06,077 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:22:02,255 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 0a859f62-6085-47be-8d87-0392bac5fe66
2025-06-14 22:22:03,165 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/token HTTP/1.1" 200 3759
2025-06-14 22:22:03,165 - msal.token_cache - DEBUG - event={
    "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
    "data": {
        "claims": null,
        "client_id": "9868b800-1d91-4573-9cf9-c2061096c3cb",
        "code": "GAQABIQEAAABVrSpeuWamRam2jAF1XRQEDJG37_yTIgkZVO4Wyq1DTv3UnA7xsEHRlV2jPoHVMpRc54zhs2vlh8AT4HMpNAZWBCD-Ybbjl6rXWhbxILN8zWyroqD8ZH6VTNhR2JrJ_4uJE2aBJOckaf9URzvMJ7Q3IvVrQo4ZGy6V4Bd2jbn2r6rcyo9RQwAZ5uJKwc9s9tAg7e9UyXeXFnuvKZfwIVIIhKKH04wnXxszWfYWzrVTFRPS9ykvy9BPa3K0-ISW0GItqIoqLWLOaZmJKX0TPnyTKjH8sld-44I1zdk9f29dj1wvSZRhZ-Z7IVLZ8zYQwQLK5fNZoc_Hlj-2Xb9PizN1GWNcH-4l5ai0jfj3_jmvOiAA",
        "device_code": "GAQABIQEAAABVrSpeuWamRam2jAF1XRQEDJG37_yTIgkZVO4Wyq1DTv3UnA7xsEHRlV2jPoHVMpRc54zhs2vlh8AT4HMpNAZWBCD-Ybbjl6rXWhbxILN8zWyroqD8ZH6VTNhR2JrJ_4uJE2aBJOckaf9URzvMJ7Q3IvVrQo4ZGy6V4Bd2jbn2r6rcyo9RQwAZ5uJKwc9s9tAg7e9UyXeXFnuvKZfwIVIIhKKH04wnXxszWfYWzrVTFRPS9ykvy9BPa3K0-ISW0GItqIoqLWLOaZmJKX0TPnyTKjH8sld-44I1zdk9f29dj1wvSZRhZ-Z7IVLZ8zYQwQLK5fNZoc_Hlj-2Xb9PizN1GWNcH-4l5ai0jfj3_jmvOiAA"
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "urn:ietf:params:oauth:grant-type:device_code",
    "params": null,
    "response": {
        "access_token": "********",
        "client_info": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "id_token": "********",
        "refresh_token": "********",
        "scope": "https://graph.microsoft.com/Mail.Send openid profile",
        "token_type": "Bearer"
    },
    "scope": [
        "https://graph.microsoft.com/Mail.Send",
        "openid",
        "profile"
    ],
    "token_endpoint": "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
}
2025-06-14 22:22:03,180 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:22:03,700 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-14 22:22:12,198 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:22:12,616 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 22:29:56,974 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 22:29:57,923 - App - INFO - Application closing - cleaning up threads
2025-06-14 22:29:57,923 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 22:30:05,677 - App - INFO - pywinauto is installed
2025-06-14 22:30:08,644 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:30:08,644 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:30:14,013 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:30:14,444 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:30:15,114 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:30:15,114 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:30:15,114 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:30:15,620 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:30:15,620 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:30:15,624 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:30:15,745 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:30:16,222 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:34:51,363 - App - INFO - pywinauto is installed
2025-06-14 22:34:54,233 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:34:54,233 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:34:59,643 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:35:00,083 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:35:00,753 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:35:00,753 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:35:00,758 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:35:01,198 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:35:01,199 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:35:01,199 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:35:01,290 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:35:01,689 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:47:36,813 - App - INFO - pywinauto is installed
2025-06-14 22:47:46,599 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:47:46,600 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:47:52,020 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:47:52,443 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:47:53,121 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:47:53,121 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:47:53,124 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:47:53,557 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:47:53,557 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:47:53,557 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:47:53,660 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:47:54,378 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:50:06,269 - App - INFO - pywinauto is installed
2025-06-14 22:50:08,948 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:50:08,948 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:50:14,558 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:50:14,908 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:50:15,588 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:50:15,593 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:50:15,595 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:50:16,054 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:50:16,055 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:50:16,055 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:50:16,143 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:50:16,495 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:51:56,816 - App - INFO - pywinauto is installed
2025-06-14 22:52:00,015 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:52:00,016 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:52:05,484 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:52:05,915 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:52:06,583 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:52:06,583 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:52:06,587 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:52:06,947 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:52:06,947 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:52:06,947 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:52:07,053 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:52:07,976 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 22:59:56,343 - App - INFO - pywinauto is installed
2025-06-14 22:59:59,453 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:59:59,453 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:00:04,903 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:00:05,327 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:00:05,998 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:00:05,998 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:00:06,001 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:00:06,436 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:00:06,437 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:00:06,437 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:00:06,535 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:00:06,905 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:02:27,203 - App - INFO - pywinauto is installed
2025-06-14 23:02:32,156 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:02:32,156 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:02:37,554 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:02:37,893 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:02:38,563 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:02:38,563 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:02:38,563 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:02:38,953 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:02:38,956 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:02:38,956 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:02:39,069 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:02:39,613 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:06:38,302 - App - INFO - pywinauto is installed
2025-06-14 23:08:36,938 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:08:36,938 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:08:42,380 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:08:42,814 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:08:43,480 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:08:43,480 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:08:43,482 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:08:43,920 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:08:43,921 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:08:43,921 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:08:44,013 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:08:44,467 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:22:24,944 - App - INFO - pywinauto is installed
2025-06-14 23:22:27,664 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:22:27,664 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:22:33,189 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:22:33,630 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:22:34,303 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:22:34,303 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:22:34,307 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:22:34,806 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:22:34,807 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:22:34,807 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:22:34,927 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:22:35,468 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:22:35,469 - oauth2_dialog - INFO - === OAUTH2 DIALOG CREATION STARTED ===
2025-06-14 23:22:35,469 - oauth2_dialog - INFO - Dialog thread ID: 28396
2025-06-14 23:22:35,469 - oauth2_dialog - INFO - Dialog thread name: Dummy-1
2025-06-14 23:22:35,469 - oauth2_dialog - INFO - User code: X2LDZZUJ
2025-06-14 23:22:35,469 - oauth2_dialog - INFO - Verification URI: https://www.microsoft.com/link
2025-06-14 23:22:35,471 - oauth2_dialog - INFO - PyQt6 imports successful
2025-06-14 23:35:47,501 - App - INFO - pywinauto is installed
2025-06-14 23:35:59,221 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:35:59,221 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:36:04,624 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:36:05,041 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:36:05,716 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:36:05,716 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:36:05,718 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:36:06,234 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:36:06,235 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:36:06,235 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:36:06,355 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:36:06,891 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:40:19,589 - App - INFO - pywinauto is installed
2025-06-14 23:40:36,243 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:40:36,243 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:40:41,753 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:40:42,184 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:40:42,865 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:40:42,865 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:40:42,869 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:40:43,302 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:40:43,303 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:40:43,303 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:40:43,409 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:40:43,797 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:40:50,193 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:40:50,539 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:40:56,124 - App - WARNING - Health check thread did not terminate within timeout
2025-06-14 23:41:03,107 - App - INFO - pywinauto is installed
2025-06-14 23:41:12,953 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:41:12,955 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:41:18,471 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:41:18,829 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:41:19,505 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:41:19,505 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:41:19,509 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:41:19,886 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:41:19,886 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:41:19,886 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:41:20,006 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:41:20,356 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:41:26,875 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:41:27,222 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:41:35,466 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:41:35,843 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 23:48:34,320 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 23:48:35,811 - App - INFO - Application closing - cleaning up threads
2025-06-14 23:48:35,811 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 23:48:39,769 - App - INFO - pywinauto is installed
2025-06-14 23:48:43,409 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:48:43,409 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:48:49,717 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:48:50,072 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 23:48:50,672 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:48:50,672 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:48:50,676 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:48:51,041 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:48:51,042 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:48:51,043 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:48:51,137 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:48:51,532 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:54:37,348 - App - INFO - pywinauto is installed
2025-06-14 23:54:40,489 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:54:40,489 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:54:46,005 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:54:46,429 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:54:47,140 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:54:47,140 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:54:47,140 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:54:47,579 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:54:47,579 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:54:47,579 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:54:47,690 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:54:48,109 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:56:10,384 - App - INFO - pywinauto is installed
2025-06-14 23:56:14,361 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:56:14,361 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:56:19,794 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:56:20,145 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:56:20,819 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:56:20,819 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:56:20,819 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:56:21,185 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:56:21,185 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:56:21,185 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:56:21,301 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:56:21,747 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" ************-06-14 23:57:26,710 - App - INFO - pywinauto is installed
2025-06-14 23:57:30,039 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 23:57:30,039 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 23:57:35,616 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 23:57:35,972 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 23:57:36,649 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 23:57:36,649 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 23:57:36,649 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 23:57:37,089 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 23:57:37,089 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 23:57:37,089 - msal.application - DEBUG - Broker enabled? None
2025-06-14 23:57:37,199 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 23:57:37,630 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" 200 635
