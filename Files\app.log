2025-05-28 11:30:45,016 - App - INFO - Application closing - cleaning up threads
2025-05-28 11:30:45,016 - App - ERROR - Error cleaning up email sending thread: wrapped C/C++ object of type QThread has been deleted
2025-05-29 00:50:18,155 - App - INFO - pywinauto is installed
2025-05-29 00:50:29,051 - App - INFO - Application closing - cleaning up threads
2025-05-29 00:50:29,051 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 17:18:43,174 - App - INFO - pywinauto is installed
2025-06-03 17:47:33,890 - App - INFO - pywinauto is installed
2025-06-03 18:01:27,727 - App - INFO - Application closing - cleaning up threads
2025-06-03 18:01:27,727 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 18:09:41,524 - App - INFO - pywinauto is installed
2025-06-03 18:10:45,940 - App - INFO - Application closing - cleaning up threads
2025-06-03 18:10:45,940 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 18:10:53,820 - App - INFO - pywinauto is installed
2025-06-03 18:12:09,934 - App - INFO - pywinauto is installed
2025-06-03 18:12:13,944 - App - INFO - Application closing - cleaning up threads
2025-06-03 18:12:13,944 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 18:12:14,508 - App - INFO - Application closing - cleaning up threads
2025-06-03 18:12:14,508 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 18:12:17,403 - App - INFO - pywinauto is installed
2025-06-03 18:16:48,401 - App - INFO - Application closing - cleaning up threads
2025-06-03 18:16:48,401 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 18:17:29,997 - App - INFO - pywinauto is installed
2025-06-03 18:18:25,812 - App - INFO - Application closing - cleaning up threads
2025-06-03 18:18:25,812 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 19:26:16,657 - App - INFO - pywinauto is installed
2025-06-03 19:26:34,428 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 19:26:34,428 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 19:26:43,704 - App - INFO - Application closing - cleaning up threads
2025-06-03 19:26:43,704 - App - INFO - Stopping email sending thread
2025-06-03 19:26:45,704 - App - WARNING - Email sending thread did not terminate within timeout
2025-06-03 19:41:41,899 - App - INFO - pywinauto is installed
2025-06-03 19:42:12,864 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 19:42:12,864 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 19:42:21,752 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:42:22,114 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 19:42:23,132 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 19:42:23,501 - Worker - INFO - Email Sent using >> leah.richardson@gmx.<NAME_EMAIL>
2025-06-03 19:47:46,712 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:47:47,063 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 19:47:48,000 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 19:47:48,376 - Worker - INFO - Email Sent using >> leah.richardson@gmx.<NAME_EMAIL>
2025-06-03 19:56:44,894 - App - INFO - Application closing - cleaning up threads
2025-06-03 19:56:44,894 - App - ERROR - Error cleaning up email sending thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 19:56:50,848 - App - INFO - pywinauto is installed
2025-06-03 19:56:56,136 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 19:56:56,136 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 19:57:05,064 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:57:05,743 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 19:57:09,111 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:57:09,642 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 19:57:20,611 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:57:20,939 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 19:57:30,512 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:57:30,814 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 19:57:37,393 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:57:37,831 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 19:57:48,819 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:57:49,178 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 19:57:52,955 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 19:58:40,375 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 19:58:40,674 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 19:58:41,618 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 19:58:41,710 - Worker - ERROR - Error Email Not Sent >> <EMAIL> Connection unexpectedly closed: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-03 19:58:48,784 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 19:58:49,254 - Worker - INFO - Email Sent using >> sharlene.pena@gmx.<NAME_EMAIL>
2025-06-03 19:58:55,379 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 19:58:55,857 - Worker - INFO - Email Sent using >> leah.richardson@gmx.<NAME_EMAIL>
2025-06-03 19:59:35,861 - App - INFO - pywinauto is installed
2025-06-03 20:01:37,739 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 20:01:37,739 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 20:01:44,803 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:01:45,252 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:01:48,633 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:01:48,976 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:01:59,909 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:02:00,286 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:07:28,495 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 20:07:29,251 - App - INFO - Application closing - cleaning up threads
2025-06-03 20:07:29,251 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 20:12:51,934 - App - INFO - pywinauto is installed
2025-06-03 20:12:55,944 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 20:12:55,944 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 20:13:04,815 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:13:05,188 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:13:11,515 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:13:12,031 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 20:13:22,545 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:13:22,905 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:18:24,768 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 20:18:25,556 - App - INFO - Application closing - cleaning up threads
2025-06-03 20:18:25,556 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 20:18:28,863 - App - INFO - pywinauto is installed
2025-06-03 20:18:33,413 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 20:18:33,413 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 20:18:42,272 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:18:42,637 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:18:48,738 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:18:49,172 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:18:59,584 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:18:59,910 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:19:21,389 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 20:23:14,460 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:23:14,748 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:23:21,272 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:23:21,789 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 20:23:32,531 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:23:32,878 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:23:35,557 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 20:24:15,738 - App - INFO - Application closing - cleaning up threads
2025-06-03 20:24:15,738 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 20:24:51,619 - App - INFO - pywinauto is installed
2025-06-03 20:24:58,251 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 20:24:58,251 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 20:24:58,251 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:24:58,547 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:24:58,547 - SimpleVPN - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-06-03 20:24:58,625 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-06-03 20:24:58,625 - SimpleVPN - INFO - VPN is already connected
2025-06-03 20:24:58,625 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:24:58,907 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:24:58,922 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:24:59,188 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:24:59,188 - SimpleVPN - DEBUG - ensure_vpn_off called, current state: vpn_enabled=True
2025-06-03 20:24:59,266 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-06-03 20:27:16,011 - App - WARNING - Bounce collection thread did not terminate within timeout
2025-06-03 20:27:27,063 - App - INFO - pywinauto is installed
2025-06-03 20:27:51,383 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 20:27:51,383 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 20:28:01,833 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:28:02,160 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:28:05,179 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:28:05,459 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:28:12,201 - App - WARNING - Health check thread did not terminate within timeout
2025-06-03 20:28:29,879 - App - INFO - pywinauto is installed
2025-06-03 20:28:33,212 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 20:28:33,212 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 20:28:33,212 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:28:33,571 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:28:33,571 - SimpleVPN - DEBUG - ensure_vpn_on called, current state: vpn_enabled=False
2025-06-03 20:28:33,649 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-06-03 20:28:33,649 - SimpleVPN - INFO - VPN is already connected
2025-06-03 20:28:33,649 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:28:33,946 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:28:33,962 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:28:34,274 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:28:34,274 - SimpleVPN - DEBUG - ensure_vpn_off called, current state: vpn_enabled=True
2025-06-03 20:28:34,352 - SimpleVPN - DEBUG - Ping test successful, VPN appears to be working
2025-06-03 20:36:56,419 - App - WARNING - Bounce collection thread did not terminate within timeout
2025-06-03 20:37:05,049 - App - INFO - pywinauto is installed
2025-06-03 20:37:26,680 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 20:37:26,680 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 20:37:35,593 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:37:35,962 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:37:52,452 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:37:52,920 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 20:38:03,409 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:38:03,754 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:38:24,494 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:38:24,793 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:38:37,959 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:38:38,391 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 20:38:48,674 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:38:49,014 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:40:09,151 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 20:40:20,863 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:40:21,165 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:40:34,660 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:40:35,176 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 20:40:46,101 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:40:46,459 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 20:48:45,169 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 20:48:46,470 - App - INFO - Application closing - cleaning up threads
2025-06-03 20:48:46,470 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 20:48:48,684 - App - INFO - pywinauto is installed
2025-06-03 20:48:52,657 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 20:48:52,657 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 20:49:04,578 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:49:05,107 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 20:49:42,951 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:49:43,390 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 20:49:54,353 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 20:49:54,681 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 21:31:02,614 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 21:31:04,145 - App - INFO - Application closing - cleaning up threads
2025-06-03 21:31:04,145 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 21:31:12,176 - App - INFO - pywinauto is installed
2025-06-03 21:31:16,873 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 21:31:16,873 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 21:31:28,878 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 21:31:29,344 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 21:31:56,055 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 21:31:56,431 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 21:32:07,332 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 21:32:07,669 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 21:51:40,456 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 21:51:56,685 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 21:51:57,205 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 21:52:19,385 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 21:52:19,879 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 21:52:30,348 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 21:52:30,743 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:02:19,539 - App - WARNING - Bounce collection thread did not terminate within timeout
2025-06-03 22:02:48,825 - App - INFO - pywinauto is installed
2025-06-03 22:02:55,822 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 22:02:55,822 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 22:03:07,576 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:03:08,097 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 22:03:29,917 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:03:30,343 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 22:03:40,950 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:03:41,305 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:04:54,075 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 22:05:46,054 - App - INFO - Application closing - cleaning up threads
2025-06-03 22:05:46,054 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 22:05:50,374 - App - INFO - pywinauto is installed
2025-06-03 22:05:57,704 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 22:05:57,704 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 22:06:09,661 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:06:10,192 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 22:06:31,512 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:06:31,950 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 22:06:42,955 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:06:43,314 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:06:44,613 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 22:06:57,147 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:06:57,449 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:06:58,451 - Worker - ERROR - Error <NAME_EMAIL> >> Connection unexpectedly closed: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-03 22:06:59,412 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 22:06:59,766 - Worker - INFO - Email Sent using >> sharlene.pena@gmx.<NAME_EMAIL>
2025-06-03 22:07:06,107 - Worker - ERROR - Error <NAME_EMAIL> >> (535, b'Authentication credentials invalid')
2025-06-03 22:07:06,953 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 22:07:07,298 - Worker - INFO - Email Sent using >> sharlene.pena@gmx.<NAME_EMAIL>
2025-06-03 22:07:13,370 - Worker - ERROR - Error <NAME_EMAIL> >> (535, b'Authentication credentials invalid')
2025-06-03 22:07:14,245 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 22:07:14,588 - Worker - INFO - Email Sent using >> sharlene.pena@gmx.<NAME_EMAIL>
2025-06-03 22:08:57,690 - App - INFO - pywinauto is installed
2025-06-03 22:09:02,217 - App - INFO - Application closing - cleaning up threads
2025-06-03 22:09:02,217 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 22:09:28,626 - App - INFO - pywinauto is installed
2025-06-03 22:09:44,930 - App - INFO - Application closing - cleaning up threads
2025-06-03 22:09:44,930 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-03 22:09:48,030 - App - INFO - pywinauto is installed
2025-06-03 22:10:18,361 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-03 22:10:18,361 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-03 22:10:30,445 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:10:30,987 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 22:10:51,587 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:10:52,018 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 22:11:03,048 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:11:03,397 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:16:26,029 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:16:26,464 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 22:16:45,189 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:16:45,557 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 22:16:56,341 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:16:56,718 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:18:20,371 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 22:18:57,028 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:18:57,313 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:18:57,924 - Worker - ERROR - Error <NAME_EMAIL> >> Server not connected
2025-06-03 22:19:00,232 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 22:19:00,692 - Worker - INFO - Email Sent using >> beth.chapman@gmx.<NAME_EMAIL>
2025-06-03 22:19:06,699 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 22:19:07,120 - Worker - INFO - Email Sent using >> beth.chapman@gmx.<NAME_EMAIL>
2025-06-03 22:19:13,139 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 22:19:13,564 - Worker - INFO - Email Sent using >> beth.chapman@gmx.<NAME_EMAIL>
2025-06-03 22:19:19,641 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 22:19:20,075 - Worker - INFO - Email Sent using >> beth.chapman@gmx.<NAME_EMAIL>
2025-06-03 22:19:26,087 - Worker - INFO - Smtp Login Success >> <EMAIL>
2025-06-03 22:19:47,116 - Worker - ERROR - Error Email Not Sent >> <EMAIL> Connection unexpectedly closed: [WinError 10054] An existing connection was forcibly closed by the remote host
2025-06-03 22:21:53,556 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:21:54,171 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 22:22:50,020 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:22:50,438 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-03 22:23:01,460 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:23:01,871 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:26:41,435 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 22:27:15,738 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:27:16,244 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 22:28:00,324 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:28:00,737 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-03 22:28:12,296 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-03 22:28:12,652 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-03 22:28:57,795 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-03 22:28:58,959 - App - INFO - Application closing - cleaning up threads
2025-06-03 22:28:58,959 - App - ERROR - Error cleaning up email sending thread: wrapped C/C++ object of type QThread has been deleted
2025-06-04 01:56:55,801 - App - INFO - pywinauto is installed
2025-06-04 01:57:08,118 - App - INFO - Application closing - cleaning up threads
2025-06-04 01:57:08,118 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 17:48:41,763 - App - INFO - pywinauto is installed
2025-06-14 18:02:30,064 - App - INFO - Application closing - cleaning up threads
2025-06-14 18:02:30,064 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 20:35:00,660 - App - INFO - pywinauto is installed
2025-06-14 20:35:05,351 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 20:35:05,351 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 20:35:13,132 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 20:35:13,486 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 20:35:20,410 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 20:35:20,930 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-14 20:35:32,076 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 20:35:32,390 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 20:35:45,620 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 20:35:45,905 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 20:35:52,670 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 20:35:53,172 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-14 20:36:03,985 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 20:36:04,300 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 20:41:40,511 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 20:41:41,826 - App - INFO - Application closing - cleaning up threads
2025-06-14 20:41:41,826 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 20:55:20,580 - App - INFO - pywinauto is installed
2025-06-14 20:55:37,024 - App - INFO - Application closing - cleaning up threads
2025-06-14 20:55:37,024 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 20:57:55,119 - App - INFO - pywinauto is installed
2025-06-14 20:57:57,133 - App - INFO - Application closing - cleaning up threads
2025-06-14 20:57:57,133 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:00:15,769 - App - INFO - pywinauto is installed
2025-06-14 21:06:01,859 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:06:01,859 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:06:08,679 - App - INFO - pywinauto is installed
2025-06-14 21:06:12,509 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:06:12,509 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:08:54,758 - App - INFO - pywinauto is installed
2025-06-14 21:09:35,178 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:09:35,178 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:09:52,600 - App - INFO - pywinauto is installed
2025-06-14 21:12:26,487 - App - INFO - pywinauto is installed
2025-06-14 21:12:27,498 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:12:27,498 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:16:47,150 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:16:47,150 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:17:24,399 - App - INFO - pywinauto is installed
2025-06-14 21:17:27,768 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:17:27,768 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:23:09,538 - App - INFO - pywinauto is installed
2025-06-14 21:23:42,702 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:23:42,702 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:25:06,087 - App - INFO - pywinauto is installed
2025-06-14 21:25:10,087 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:25:10,087 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:25:21,672 - App - INFO - pywinauto is installed
2025-06-14 21:25:28,577 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 21:25:28,577 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 21:25:37,749 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 21:25:38,180 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 21:25:39,000 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 21:25:39,000 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/common
2025-06-14 21:25:39,003 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 21:25:39,520 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1547
2025-06-14 21:25:39,527 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/common/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/{tenantid}/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/common/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 21:25:39,527 - msal.application - DEBUG - Broker enabled? None
2025-06-14 21:25:39,647 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 21:25:39,807 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /common/oauth2/v2.0/devicecode HTTP/1.1" 400 459
2025-06-14 21:25:45,237 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 21:25:45,237 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/common
2025-06-14 21:25:45,237 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 21:25:45,687 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1547
2025-06-14 21:25:45,687 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/common/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/{tenantid}/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/common/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 21:25:45,687 - msal.application - DEBUG - Broker enabled? None
2025-06-14 21:25:45,802 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 21:25:45,957 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /common/oauth2/v2.0/devicecode HTTP/1.1" 400 459
2025-06-14 21:25:51,367 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 21:25:51,367 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/common
2025-06-14 21:25:51,367 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 21:25:51,812 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1547
2025-06-14 21:25:51,812 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/common/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/{tenantid}/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/common/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 21:25:51,812 - msal.application - DEBUG - Broker enabled? None
2025-06-14 21:25:51,933 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 21:25:52,062 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /common/oauth2/v2.0/devicecode HTTP/1.1" 400 459
2025-06-14 21:25:55,531 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 21:25:55,537 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/common
2025-06-14 21:25:55,537 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 21:25:55,957 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1547
2025-06-14 21:25:55,957 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/common/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/{tenantid}/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/common/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 21:25:55,957 - msal.application - DEBUG - Broker enabled? None
2025-06-14 21:25:56,087 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 21:25:56,229 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /common/oauth2/v2.0/devicecode HTTP/1.1" 400 459
2025-06-14 21:25:59,200 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 21:25:59,615 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 21:26:07,853 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 21:26:08,381 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 21:28:00,517 - App - ERROR - Error cleaning up bounce collection thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 21:28:01,456 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:28:01,456 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 21:28:35,487 - App - INFO - pywinauto is installed
2025-06-14 21:28:40,722 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 21:28:40,722 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 21:28:46,894 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 21:28:47,180 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 21:28:47,655 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 21:28:47,655 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/common
2025-06-14 21:28:47,658 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 21:28:48,183 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1547
2025-06-14 21:28:48,184 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/common/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/common/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/{tenantid}/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/common/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/common/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 21:28:48,184 - msal.application - DEBUG - Broker enabled? None
2025-06-14 21:28:48,300 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 21:28:48,621 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 21:28:49,440 - SimpleVPN - DEBUG - Current IP address: ***************
2025-06-14 21:28:57,740 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 21:28:58,076 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 21:42:35,818 - App - ERROR - Error cleaning up health check thread: wrapped C/C++ object of type QThread has been deleted
2025-06-14 21:42:40,669 - App - INFO - Application closing - cleaning up threads
2025-06-14 21:42:40,669 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 22:19:44,480 - App - INFO - pywinauto is installed
2025-06-14 22:19:46,446 - App - INFO - Application closing - cleaning up threads
2025-06-14 22:19:46,446 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
2025-06-14 22:27:22,377 - App - INFO - pywinauto is installed
2025-06-14 22:28:16,934 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:28:16,940 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:28:23,098 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:28:23,442 - SimpleVPN - DEBUG - Current IP address: **************
2025-06-14 22:28:23,941 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:28:23,941 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:28:23,945 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:28:24,690 - urllib3.util.retry - DEBUG - Incremented Retry for (url='/consumers/v2.0/.well-known/openid-configuration'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:28:24,690 - urllib3.connectionpool - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None))': /consumers/v2.0/.well-known/openid-configuration
2025-06-14 22:28:24,690 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (2): login.microsoftonline.com:443
2025-06-14 22:28:25,336 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:28:25,337 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:28:25,337 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:28:25,451 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:28:27,014 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" 200 635
2025-06-14 22:40:46,796 - App - INFO - pywinauto is installed
2025-06-14 22:42:19,642 - SimpleVPN - INFO - SimpleVPNManager initialized with change_ip_after=5
2025-06-14 22:42:19,642 - SimpleVPN - INFO - HMA VPN found at: C:\Program Files\Privax\HMA VPN\Vpn.exe
2025-06-14 22:42:25,042 - SimpleVPN - DEBUG - Getting current public IP address...
2025-06-14 22:42:25,469 - SimpleVPN - DEBUG - Current IP address: ************
2025-06-14 22:42:26,117 - urllib3.util.retry - DEBUG - Converted retries value: 1 -> Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-06-14 22:42:26,117 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/consumers
2025-06-14 22:42:26,120 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-14 22:42:26,614 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /consumers/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1591
2025-06-14 22:42:26,615 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/consumers/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/consumers/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/9188040d-6c67-4c5b-b112-36a304b66dad/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/consumers/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/consumers/kerberos', 'tenant_region_scope': None, 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-14 22:42:26,615 - msal.application - DEBUG - Broker enabled? None
2025-06-14 22:42:26,749 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize HTTP/1.1" ************-06-14 22:42:27,181 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /consumers/oauth2/v2.0/devicecode HTTP/1.1" 200 635
2025-06-15 01:02:27,302 - App - INFO - pywinauto is installed
2025-06-15 01:02:30,934 - App - INFO - Application closing - cleaning up threads
2025-06-15 01:02:30,934 - App - ERROR - Error cleaning up email sending thread: 'builtin_function_or_method' object has no attribute 'isRunning'
