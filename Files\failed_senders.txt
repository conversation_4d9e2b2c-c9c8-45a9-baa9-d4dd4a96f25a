2025-06-15 00:51:59 | <EMAIL> | Send failed: (530, b'5.7.57 Client not authenticated to send mail. [PR2P264CA0041.FRAP264.PROD.OUTLOOK.COM 2025-06-14T23:51:58.900Z 08DDAB624CFBC948]', '<EMAIL>')
2025-06-15 01:01:03 | <EMAIL> | Health check: Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')). App password error: Connection unexpectedly closed
2025-06-15 01:01:27 | <EMAIL> | Health check: Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: Failed to create device flow. App password error: (535, b'5.7.139 Authentication unsuccessful, basic authentication is disabled. [MA2P292CA0006.ESPP292.PROD.OUTLOOK.COM 2025-06-15T00:01:27.094Z 08DDAA9B7891D9A2]')
2025-06-15 01:02:08 | <EMAIL> | Health check: Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: Failed to create device flow. App password error: Connection unexpectedly closed
2025-06-15 01:02:53 | <EMAIL> | Health check: Both OAuth2 and app password authentication failed. OAuth2 error: OAuth2 authentication failed: Failed to create device flow. App password error: (535, b'5.7.139 Authentication unsuccessful, basic authentication is disabled. [PA7P264CA0054.FRAP264.PROD.OUTLOOK.COM 2025-06-15T00:02:53.117Z 08DDAB441D474CCB]')
